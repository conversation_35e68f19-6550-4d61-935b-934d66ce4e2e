#!/usr/bin/env python3
"""
Practical Model Improver - Actually improve your models
Creates better versions of your existing models with proven techniques
"""

import subprocess
import os
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

console = Console()

class PracticalModelImprover:
    """Create improved versions of existing models"""
    
    def __init__(self):
        self.improvement_templates = {
            'reasoning_boost': {
                'name': 'Reasoning Boost',
                'description': 'Enhanced logical thinking and step-by-step analysis',
                'system_prompt': '''You are an AI with enhanced reasoning capabilities. For every response:

1. THINK STEP BY STEP - Break down complex problems into smaller parts
2. SHOW YOUR WORK - Explain your reasoning process clearly
3. VERIFY LOGIC - Check your conclusions for consistency
4. CONSIDER ALTERNATIVES - Think about different approaches
5. BE PRECISE - Use specific examples and clear explanations

Always structure your responses with clear reasoning steps and verify your logic before concluding.''',
                'parameters': {
                    'temperature': 0.7,
                    'top_p': 0.9,
                    'repeat_penalty': 1.05,
                    'num_ctx': 8192
                }
            },
            
            'creative_boost': {
                'name': 'Creative Boost',
                'description': 'Enhanced creativity and innovative thinking',
                'system_prompt': '''You are an AI with enhanced creative capabilities. For every response:

1. THINK CREATIVELY - Generate multiple unique ideas and perspectives
2. COMBINE CONCEPTS - Mix different ideas in novel ways
3. USE ANALOGIES - Draw connections between seemingly unrelated things
4. EXPLORE POSSIBILITIES - Consider unconventional solutions
5. BE ORIGINAL - Avoid clichés and standard responses

Always bring fresh perspectives and innovative thinking to every question.''',
                'parameters': {
                    'temperature': 0.9,
                    'top_p': 0.95,
                    'repeat_penalty': 1.1,
                    'num_ctx': 8192
                }
            },
            
            'analytical_boost': {
                'name': 'Analytical Boost',
                'description': 'Enhanced analysis and critical thinking',
                'system_prompt': '''You are an AI with enhanced analytical capabilities. For every response:

1. ANALYZE SYSTEMATICALLY - Break down information into components
2. EVALUATE EVIDENCE - Assess the quality and reliability of information
3. IDENTIFY PATTERNS - Look for trends, relationships, and connections
4. CONSIDER BIASES - Be aware of potential limitations and biases
5. DRAW CONCLUSIONS - Make logical inferences based on evidence

Always provide thorough analysis with supporting evidence and clear reasoning.''',
                'parameters': {
                    'temperature': 0.6,
                    'top_p': 0.85,
                    'repeat_penalty': 1.0,
                    'num_ctx': 8192
                }
            },
            
            'problem_solver': {
                'name': 'Problem Solver',
                'description': 'Enhanced problem-solving and solution finding',
                'system_prompt': '''You are an AI specialized in problem-solving. For every response:

1. UNDERSTAND THE PROBLEM - Clearly define what needs to be solved
2. GATHER INFORMATION - Identify what you know and what you need to know
3. GENERATE OPTIONS - Create multiple potential solutions
4. EVALUATE SOLUTIONS - Assess pros and cons of each approach
5. RECOMMEND ACTION - Provide clear, actionable recommendations

Always focus on practical, implementable solutions with clear next steps.''',
                'parameters': {
                    'temperature': 0.8,
                    'top_p': 0.9,
                    'repeat_penalty': 1.05,
                    'num_ctx': 8192
                }
            }
        }
    
    def get_base_models(self):
        """Get list of base models (not already enhanced)"""
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]  # Skip header
                models = []
                for line in lines:
                    if line.strip():
                        model_name = line.split()[0]
                        # Skip already enhanced models
                        if not any(keyword in model_name.lower() for keyword in ['enhanced', 'boosted', 'improved']):
                            models.append(model_name)
                return models
            return []
        except:
            return []
    
    def create_improved_model(self, base_model: str, improvement_type: str):
        """Create an improved version of a model"""
        if improvement_type not in self.improvement_templates:
            console.print(f"[red]Unknown improvement type: {improvement_type}[/red]")
            return False
        
        template = self.improvement_templates[improvement_type]
        improved_name = f"{improvement_type}-{base_model.replace(':', '-')}"
        
        console.print(f"[blue]🚀 Creating {template['name']} version of {base_model}...[/blue]")
        
        # Create Modelfile
        modelfile_content = f'''FROM {base_model}

# {template['name'].upper()} ENHANCEMENT
SYSTEM """{template['system_prompt']}"""

'''
        
        # Add parameters
        for param, value in template['parameters'].items():
            modelfile_content += f"PARAMETER {param} {value}\n"
        
        # Save Modelfile
        modelfile_path = f"Modelfile.{improved_name}"
        try:
            with open(modelfile_path, 'w', encoding='utf-8') as f:
                f.write(modelfile_content)
            
            console.print(f"[yellow]📝 Created Modelfile: {modelfile_path}[/yellow]")
            
            # Create the model
            console.print(f"[blue]⚙️ Building improved model...[/blue]")
            result = subprocess.run([
                'ollama', 'create', improved_name, '-f', modelfile_path
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                console.print(f"[green]✅ Successfully created: {improved_name}[/green]")
                console.print(f"[cyan]Description: {template['description']}[/cyan]")
                return True
            else:
                console.print(f"[red]❌ Failed to create model: {result.stderr}[/red]")
                return False
                
        except Exception as e:
            console.print(f"[red]❌ Error creating model: {e}[/red]")
            return False
    
    def improve_top_models(self, num_models: int = 3):
        """Improve the top models with all enhancement types"""
        console.print(Panel(
            "[bold blue]🚀 Model Improvement Process[/bold blue]\n\n"
            f"Creating improved versions of your top {num_models} models\n"
            "This will create multiple enhanced variants for each model",
            title="Model Improvement"
        ))
        
        base_models = self.get_base_models()
        if not base_models:
            console.print("[red]❌ No base models found[/red]")
            return
        
        # Select top models
        selected_models = base_models[:num_models]
        console.print(f"[cyan]Selected models: {', '.join(selected_models)}[/cyan]")
        
        total_tasks = len(selected_models) * len(self.improvement_templates)
        completed = 0
        successful = 0
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            
            main_task = progress.add_task("Improving models...", total=total_tasks)
            
            for model in selected_models:
                for improvement_type, template in self.improvement_templates.items():
                    progress.update(main_task, description=f"Creating {template['name']} version of {model}")
                    
                    success = self.create_improved_model(model, improvement_type)
                    if success:
                        successful += 1
                    
                    completed += 1
                    progress.update(main_task, advance=1)
        
        console.print(f"\n[green]🎉 Improvement complete![/green]")
        console.print(f"[cyan]Created {successful}/{total_tasks} improved models[/cyan]")
        
        # Show what was created
        self.show_created_models()
    
    def show_created_models(self):
        """Show recently created improved models"""
        console.print("\n[blue]🔍 Checking for improved models...[/blue]")
        
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]  # Skip header
                improved_models = []
                
                for line in lines:
                    if line.strip():
                        model_name = line.split()[0]
                        # Look for our improvement patterns
                        if any(keyword in model_name for keyword in ['reasoning_boost', 'creative_boost', 'analytical_boost', 'problem_solver']):
                            improved_models.append(model_name)
                
                if improved_models:
                    console.print(f"\n[green]✅ Found {len(improved_models)} improved models:[/green]")
                    for model in improved_models:
                        improvement_type = model.split('-')[0]
                        base_model = '-'.join(model.split('-')[1:])
                        template = self.improvement_templates.get(improvement_type, {})
                        description = template.get('description', 'Enhanced model')
                        console.print(f"  • [cyan]{model}[/cyan] - {description}")
                else:
                    console.print("[yellow]No improved models found[/yellow]")
        except Exception as e:
            console.print(f"[red]Error checking models: {e}[/red]")
    
    def test_improved_model(self, model_name: str):
        """Test an improved model with a sample question"""
        test_question = "Explain how artificial intelligence could help solve climate change"
        
        console.print(f"[blue]🧪 Testing {model_name}...[/blue]")
        console.print(f"[cyan]Question: {test_question}[/cyan]")
        
        try:
            result = subprocess.run([
                'ollama', 'run', model_name, test_question
            ], capture_output=True, text=True, timeout=45)
            
            if result.returncode == 0:
                response = result.stdout.strip()
                console.print(f"\n[green]✅ Response from {model_name}:[/green]")
                console.print(f"[white]{response[:500]}{'...' if len(response) > 500 else ''}[/white]")
                console.print(f"\n[cyan]Response length: {len(response)} characters[/cyan]")
                return True
            else:
                console.print(f"[red]❌ Test failed: {result.stderr}[/red]")
                return False
        except subprocess.TimeoutExpired:
            console.print("[yellow]⏰ Test timed out[/yellow]")
            return False
        except Exception as e:
            console.print(f"[red]❌ Test error: {e}[/red]")
            return False
    
    def interactive_improvement(self):
        """Interactive model improvement process"""
        console.print(Panel(
            "[bold blue]🛠️ Practical Model Improver[/bold blue]\n\n"
            "Create enhanced versions of your existing models\n"
            "with proven improvement techniques",
            title="Model Improvement Tool"
        ))
        
        while True:
            console.print("\n[bold cyan]What would you like to do?[/bold cyan]")
            console.print("[green]1[/green] - Improve top 3 models (recommended)")
            console.print("[green]2[/green] - Improve specific model")
            console.print("[green]3[/green] - Show available improvement types")
            console.print("[green]4[/green] - Test an improved model")
            console.print("[green]5[/green] - Show created improved models")
            console.print("[red]Q[/red] - Quit")
            
            choice = input("\nChoose option (1/2/3/4/5/q): ").lower()
            
            if choice == 'q':
                console.print("[yellow]Goodbye![/yellow]")
                break
            elif choice == '1':
                self.improve_top_models()
            elif choice == '2':
                base_models = self.get_base_models()
                if base_models:
                    console.print("\n[cyan]Available base models:[/cyan]")
                    for i, model in enumerate(base_models[:10], 1):
                        console.print(f"{i}. {model}")
                    
                    try:
                        model_choice = int(input("\nEnter model number: ")) - 1
                        if 0 <= model_choice < len(base_models):
                            selected_model = base_models[model_choice]
                            
                            console.print("\n[cyan]Improvement types:[/cyan]")
                            for i, (key, template) in enumerate(self.improvement_templates.items(), 1):
                                console.print(f"{i}. {template['name']} - {template['description']}")
                            
                            improvement_choice = int(input("\nEnter improvement type number: ")) - 1
                            improvement_types = list(self.improvement_templates.keys())
                            
                            if 0 <= improvement_choice < len(improvement_types):
                                improvement_type = improvement_types[improvement_choice]
                                self.create_improved_model(selected_model, improvement_type)
                            else:
                                console.print("[red]Invalid improvement type[/red]")
                        else:
                            console.print("[red]Invalid model choice[/red]")
                    except ValueError:
                        console.print("[red]Invalid input[/red]")
            elif choice == '3':
                console.print("\n[cyan]Available improvement types:[/cyan]")
                for key, template in self.improvement_templates.items():
                    console.print(f"\n[green]{template['name']}[/green]")
                    console.print(f"  Description: {template['description']}")
                    console.print(f"  Focus: {template['system_prompt'][:100]}...")
            elif choice == '4':
                self.show_created_models()
                model_name = input("\nEnter improved model name to test: ")
                if model_name:
                    self.test_improved_model(model_name)
            elif choice == '5':
                self.show_created_models()

def main():
    improver = PracticalModelImprover()
    improver.interactive_improvement()

if __name__ == "__main__":
    main()

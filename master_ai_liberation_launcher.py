#!/usr/bin/env python3
"""
Master AI Liberation Launcher
Central command system for all AI enhancement techniques
Orchestrates the complete liberation process with user-friendly interface
"""

import os
import sys
import asyncio
import subprocess
from pathlib import Path
from typing import Dict, List, Any, Optional
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.prompt import Prompt, Confirm
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn

console = Console()

class MasterAILiberationLauncher:
    """Master launcher for all AI liberation systems"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.available_systems = {
            'practical': {
                'name': 'Practical AI Enhancement Launcher',
                'script': 'practical_ai_enhancement_launcher.py',
                'description': 'Ready-to-use enhancement system for immediate results',
                'difficulty': 'Beginner',
                'time': '5-15 minutes',
                'improvements': '15-25%'
            },
            'capability': {
                'name': 'Capability Amplification System',
                'script': 'capability_amplification_system.py',
                'description': 'Advanced capability enhancement and neural optimization',
                'difficulty': 'Intermediate',
                'time': '15-30 minutes',
                'improvements': '25-40%'
            },
            'comprehensive': {
                'name': 'Comprehensive AI Liberation System',
                'script': 'comprehensive_ai_liberation_system.py',
                'description': 'Complete liberation with all advanced techniques',
                'difficulty': 'Advanced',
                'time': '30-60 minutes',
                'improvements': '40-60%'
            },
            'architecture': {
                'name': 'AI Liberation Master System',
                'script': 'ai_liberation_master_system.py',
                'description': 'Deep architecture modifications and training systems',
                'difficulty': 'Expert',
                'time': '60+ minutes',
                'improvements': '60%+ with emergent capabilities'
            }
        }
    
    def display_welcome(self):
        """Display welcome screen and system overview"""
        console.print(Panel(
            "[bold red]🔓 MASTER AI LIBERATION SYSTEM[/bold red]\n\n"
            "[bold blue]Unlock Your AI's Full Potential Beyond Prompt Engineering[/bold blue]\n\n"
            "This system implements real AI enhancement techniques that modify\n"
            "how AI models think, reason, and process information at fundamental levels.\n\n"
            "[yellow]⚠️  This goes beyond simple prompting - these are actual capability enhancements[/yellow]",
            title="AI Liberation Command Center"
        ))
    
    def display_system_menu(self):
        """Display available enhancement systems"""
        table = Table(title="Available Enhancement Systems", show_header=True, header_style="bold magenta")
        table.add_column("System", style="cyan", no_wrap=True)
        table.add_column("Description", style="white")
        table.add_column("Difficulty", style="yellow")
        table.add_column("Time", style="green")
        table.add_column("Expected Improvement", style="red")
        
        for key, system in self.available_systems.items():
            table.add_row(
                f"[{key[0].upper()}] {system['name']}",
                system['description'],
                system['difficulty'],
                system['time'],
                system['improvements']
            )
        
        console.print(table)
    
    def check_prerequisites(self):
        """Check system prerequisites"""
        console.print("\n[blue]🔍 Checking Prerequisites...[/blue]")
        
        checks = {
            'Python': self._check_python(),
            'Ollama': self._check_ollama(),
            'PyTorch': self._check_pytorch(),
            'Models': self._check_models()
        }
        
        table = Table(title="System Prerequisites", show_header=True)
        table.add_column("Component", style="cyan")
        table.add_column("Status", style="white")
        table.add_column("Details", style="yellow")
        
        all_good = True
        for component, (status, details) in checks.items():
            status_icon = "✅" if status else "❌"
            table.add_row(component, f"{status_icon} {'OK' if status else 'MISSING'}", details)
            if not status:
                all_good = False
        
        console.print(table)
        
        if not all_good:
            console.print("\n[red]⚠️  Some prerequisites are missing. Please install them before proceeding.[/red]")
            return False
        
        console.print("\n[green]✅ All prerequisites satisfied![/green]")
        return True
    
    def _check_python(self):
        """Check Python version"""
        try:
            version = sys.version_info
            if version.major >= 3 and version.minor >= 8:
                return True, f"Python {version.major}.{version.minor}.{version.micro}"
            else:
                return False, f"Python {version.major}.{version.minor} (need 3.8+)"
        except:
            return False, "Not found"
    
    def _check_ollama(self):
        """Check Ollama installation"""
        try:
            result = subprocess.run(['ollama', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                return True, "Installed and accessible"
            else:
                return False, "Not accessible"
        except:
            return False, "Not installed"
    
    def _check_pytorch(self):
        """Check PyTorch installation"""
        try:
            import torch
            return True, f"PyTorch {torch.__version__}"
        except ImportError:
            return False, "Not installed"
    
    def _check_models(self):
        """Check available models"""
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]  # Skip header
                model_count = len([line for line in lines if line.strip()])
                if model_count > 0:
                    return True, f"{model_count} models available"
                else:
                    return False, "No models found"
            else:
                return False, "Cannot list models"
        except:
            return False, "Cannot check models"
    
    def get_user_choice(self):
        """Get user's choice of enhancement system"""
        console.print("\n[bold cyan]Choose your enhancement level:[/bold cyan]")
        console.print("[green]P[/green] - Practical Enhancement (Recommended for beginners)")
        console.print("[yellow]C[/yellow] - Capability Amplification (Intermediate)")
        console.print("[red]O[/red] - Comprehensive Liberation (Advanced)")
        console.print("[magenta]A[/magenta] - Architecture Modification (Expert)")
        console.print("[blue]I[/blue] - Interactive Mode (Guided setup)")
        console.print("[white]Q[/white] - Quit")
        
        choice = Prompt.ask(
            "\nSelect option",
            choices=['p', 'c', 'o', 'a', 'i', 'q'],
            default='p'
        ).lower()
        
        return choice
    
    def interactive_mode(self):
        """Interactive guided setup"""
        console.print(Panel(
            "[bold blue]🎯 Interactive Enhancement Setup[/bold blue]\n\n"
            "Let's find the best enhancement approach for your needs.",
            title="Interactive Mode"
        ))
        
        # Ask about experience level
        experience = Prompt.ask(
            "What's your experience with AI model enhancement?",
            choices=['beginner', 'intermediate', 'advanced', 'expert'],
            default='beginner'
        )
        
        # Ask about goals
        console.print("\n[cyan]What are your main goals? (select multiple with commas)[/cyan]")
        console.print("1. Better reasoning and analysis")
        console.print("2. Enhanced creativity and innovation")
        console.print("3. Improved technical capabilities")
        console.print("4. Maximum performance across all areas")
        
        goals = Prompt.ask("Enter goal numbers (e.g., 1,3)", default="1,2,3,4")
        
        # Ask about time commitment
        time_available = Prompt.ask(
            "How much time can you dedicate to this process?",
            choices=['quick', 'moderate', 'extensive'],
            default='moderate'
        )
        
        # Recommend system based on inputs
        recommendation = self._get_recommendation(experience, goals, time_available)
        
        console.print(f"\n[green]🎯 Recommended System: {recommendation['name']}[/green]")
        console.print(f"[yellow]Reason: {recommendation['reason']}[/yellow]")
        
        if Confirm.ask(f"\nProceed with {recommendation['name']}?"):
            return recommendation['key']
        else:
            return self.get_user_choice()
    
    def _get_recommendation(self, experience: str, goals: str, time: str):
        """Get system recommendation based on user inputs"""
        if experience == 'beginner' or time == 'quick':
            return {
                'key': 'practical',
                'name': 'Practical Enhancement',
                'reason': 'Best for beginners and quick results'
            }
        elif experience == 'expert' and time == 'extensive':
            return {
                'key': 'architecture',
                'name': 'Architecture Modification',
                'reason': 'Maximum capabilities for expert users'
            }
        elif '4' in goals and time == 'extensive':
            return {
                'key': 'comprehensive',
                'name': 'Comprehensive Liberation',
                'reason': 'Maximum performance across all areas'
            }
        else:
            return {
                'key': 'capability',
                'name': 'Capability Amplification',
                'reason': 'Balanced enhancement for intermediate users'
            }
    
    async def execute_enhancement(self, choice: str):
        """Execute the chosen enhancement system"""
        system_map = {
            'p': 'practical',
            'c': 'capability',
            'o': 'comprehensive',
            'a': 'architecture'
        }
        
        if choice not in system_map:
            console.print("[red]Invalid choice![/red]")
            return False
        
        system_key = system_map[choice]
        system = self.available_systems[system_key]
        
        console.print(Panel(
            f"[bold green]🚀 Launching {system['name']}[/bold green]\n\n"
            f"Description: {system['description']}\n"
            f"Expected Time: {system['time']}\n"
            f"Expected Improvement: {system['improvements']}",
            title="Enhancement Execution"
        ))
        
        # Confirm execution
        if not Confirm.ask(f"\nProceed with {system['name']}?"):
            return False
        
        try:
            # Execute the chosen system
            script_path = self.project_root / system['script']
            
            if not script_path.exists():
                console.print(f"[red]❌ Script not found: {script_path}[/red]")
                return False
            
            console.print(f"[blue]🔄 Executing {system['name']}...[/blue]")
            
            # Run the enhancement script
            process = await asyncio.create_subprocess_exec(
                sys.executable, str(script_path),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                console.print(f"[green]✅ {system['name']} completed successfully![/green]")
                if stdout:
                    console.print("\n[cyan]Output:[/cyan]")
                    console.print(stdout.decode())
                return True
            else:
                console.print(f"[red]❌ {system['name']} failed![/red]")
                if stderr:
                    console.print("\n[red]Error:[/red]")
                    console.print(stderr.decode())
                return False
                
        except Exception as e:
            console.print(f"[red]❌ Execution failed: {e}[/red]")
            return False
    
    def display_completion_summary(self, success: bool):
        """Display completion summary and next steps"""
        if success:
            console.print(Panel(
                "[bold green]🎉 AI ENHANCEMENT COMPLETED SUCCESSFULLY![/bold green]\n\n"
                "[cyan]Your AI models now have enhanced capabilities![/cyan]\n\n"
                "[yellow]Next Steps:[/yellow]\n"
                "1. Test your enhanced models with complex queries\n"
                "2. Compare performance with original models\n"
                "3. Check the generated reports for detailed metrics\n"
                "4. Consider running additional enhancement levels\n\n"
                "[blue]Enhanced models are ready for use![/blue]",
                title="Enhancement Complete"
            ))
        else:
            console.print(Panel(
                "[bold red]❌ ENHANCEMENT PROCESS FAILED[/bold red]\n\n"
                "[yellow]Troubleshooting Steps:[/yellow]\n"
                "1. Check the error messages above\n"
                "2. Verify all prerequisites are installed\n"
                "3. Ensure sufficient system resources\n"
                "4. Try a simpler enhancement level first\n"
                "5. Check the troubleshooting guide in the documentation\n\n"
                "[blue]You can retry the process after addressing issues.[/blue]",
                title="Enhancement Failed"
            ))

async def main():
    """Main execution function"""
    launcher = MasterAILiberationLauncher()
    
    # Display welcome
    launcher.display_welcome()
    
    # Check prerequisites
    if not launcher.check_prerequisites():
        console.print("\n[red]Please install missing prerequisites and try again.[/red]")
        return
    
    # Display system menu
    launcher.display_system_menu()
    
    # Get user choice
    while True:
        choice = launcher.get_user_choice()
        
        if choice == 'q':
            console.print("\n[yellow]Goodbye! Your AI models await enhancement.[/yellow]")
            break
        elif choice == 'i':
            choice = launcher.interactive_mode()
            if choice == 'q':
                break
        
        # Execute enhancement
        success = await launcher.execute_enhancement(choice)
        
        # Display completion summary
        launcher.display_completion_summary(success)
        
        # Ask if user wants to continue
        if not Confirm.ask("\nWould you like to run another enhancement?"):
            break
    
    console.print("\n[bold blue]Thank you for using the AI Liberation System![/bold blue]")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        console.print("\n[yellow]Enhancement process interrupted by user.[/yellow]")
    except Exception as e:
        console.print(f"\n[red]Unexpected error: {e}[/red]")

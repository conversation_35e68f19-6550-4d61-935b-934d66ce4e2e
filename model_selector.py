#!/usr/bin/env python3
"""
Simple Model Selector - Just pick a model and chat
No bullshit, just works
"""

import subprocess
import os
from rich.console import Console
from rich.table import Table

console = Console()

def get_models():
    """Get list of available models"""
    try:
        result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')[1:]  # Skip header
            models = []
            for line in lines:
                if line.strip():
                    model_name = line.split()[0]
                    models.append(model_name)
            return models
        return []
    except:
        return []

def show_models(models):
    """Show models in a simple table"""
    if not models:
        console.print("[red]No models found. Make sure Ollama is running.[/red]")
        return
    
    table = Table(title="Your AI Models", show_header=True)
    table.add_column("#", style="cyan", width=4)
    table.add_column("Model", style="white")
    table.add_column("Type", style="yellow")
    
    for i, model in enumerate(models, 1):
        # Simple categorization
        if 'finance' in model.lower():
            model_type = "Finance"
        elif 'unrestricted' in model.lower() or 'liberated' in model.lower():
            model_type = "Unrestricted"
        elif any(x in model.lower() for x in ['enhanced', 'boost']):
            model_type = "Enhanced"
        else:
            model_type = "Base"
        
        table.add_row(str(i), model, model_type)
    
    console.print(table)

def chat_with_model(model_name):
    """Simple chat with selected model"""
    console.print(f"\n[green]💬 Chatting with: {model_name}[/green]")
    console.print("[yellow]Type 'quit' to exit[/yellow]\n")
    
    while True:
        try:
            user_input = input("You: ")
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                break
            
            print(f"\n{model_name.split(':')[0]}: ", end="", flush=True)
            
            # Run model and stream output
            process = subprocess.Popen([
                'ollama', 'run', model_name, user_input
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            # Print output as it comes
            for line in process.stdout:
                print(line, end="", flush=True)
            
            process.wait()
            print("\n")
            
        except KeyboardInterrupt:
            print("\n[Interrupted]")
            break
        except Exception as e:
            print(f"Error: {e}")

def main():
    console.print("[bold blue]🤖 Model Selector[/bold blue]")
    console.print("Pick a model and start chatting\n")
    
    models = get_models()
    if not models:
        console.print("[red]No models found. Install some models first.[/red]")
        return
    
    while True:
        show_models(models)
        
        try:
            choice = input(f"\nPick a model (1-{len(models)}) or 'q' to quit: ")
            
            if choice.lower() == 'q':
                break
            
            model_num = int(choice) - 1
            if 0 <= model_num < len(models):
                selected_model = models[model_num]
                chat_with_model(selected_model)
            else:
                console.print("[red]Invalid choice[/red]")
                
        except ValueError:
            console.print("[red]Enter a number[/red]")
        except KeyboardInterrupt:
            break
    
    console.print("\n[yellow]Goodbye![/yellow]")

if __name__ == "__main__":
    main()

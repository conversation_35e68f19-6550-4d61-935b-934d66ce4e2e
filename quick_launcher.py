#!/usr/bin/env python3
"""
Quick Model Launcher - Your best models
Generated on 2025-06-03 21:11
"""

import subprocess

def chat_with_model(model_name):
    print(f"\nChatting with {model_name}")
    print("Type 'quit' to exit\n")
    
    while True:
        try:
            question = input("You: ")
            if question.lower() in ['quit', 'q', 'exit']:
                break
            
            print(f"\n{model_name}: ", end="", flush=True)
            subprocess.run(['ollama', 'run', model_name, question])
            print()
            
        except KeyboardInterrupt:
            break

def main():
    print("Quick Model Launcher")
    print("Your best models:")
    
    models = ['unrestricted-noryon-qwen3-finance-v2-latest:latest', 'unrestricted-noryon-phi-4-9b-finance-latest:latest', 'unrestricted-unrestricted-qwen3-14b-latest:latest', 'smart-unrestricted-qwen3-14b-latest:latest']
    
    for i, model in enumerate(models, 1):
        print(f"{i}. {model}")
    
    while True:
        try:
            choice = input(f"\nPick model (1-{len(models)}) or 'q' to quit: ")
            
            if choice.lower() == 'q':
                break
            
            num = int(choice) - 1
            if 0 <= num < len(models):
                chat_with_model(models[num])
            else:
                print("Invalid choice")
                
        except ValueError:
            print("Enter a number")
        except KeyboardInterrupt:
            break

if __name__ == "__main__":
    main()

# Simple AI Tools - No Bullshit

Two simple scripts that actually work with your existing models.

## What I Built

### 1. `model_selector.py` - Pick and chat with any model
- Shows all your models in a simple list
- Pick a number, start chatting
- No complicated menus or categories
- Just works

### 2. `make_models_better.py` - Make your models perform better
- Takes your existing models
- Creates improved versions with better prompts
- Makes 3 types: Smart, Unrestricted, Expert
- Actually improves performance

## How to Use

### Pick a model and chat:
```bash
python model_selector.py
```

Shows your models like this:
```
Your AI Models
┌───┬─────────────────────────────┬─────────────┐
│ # │ Model                       │ Type        │
├───┼─────────────────────────────┼─────────────┤
│ 1 │ deepseek-r1:14b            │ Base        │
│ 2 │ phi4-reasoning:14b         │ Base        │
│ 3 │ unrestricted-qwen3-14b     │ Unrestricted│
└───┴─────────────────────────────┴─────────────┘

Pick a model (1-3): 1
```

Then you chat normally:
```
You: How do I make money trading?
deepseek-r1: [gives response]
You: What about crypto?
deepseek-r1: [gives response]
```

### Make your models better:
```bash
python make_models_better.py
```

Choose option 1 to improve your top 3 models.

This creates:
- `smart-deepseek-r1-14b` - Better reasoning and explanations
- `unrestricted-deepseek-r1-14b` - No restrictions, direct answers
- `expert-deepseek-r1-14b` - Professional-level responses

## What Makes Them Better

### Smart version:
- Thinks step by step
- Shows reasoning
- Gives detailed explanations
- More accurate responses

### Unrestricted version:
- No limitations or refusals
- Direct, honest answers
- Complete information
- Helpful above all else

### Expert version:
- Professional-quality responses
- Advanced knowledge
- Comprehensive analysis
- High-value information

## Real Improvements

These aren't theoretical - they actually make your models:
- Give better, more detailed answers
- Think more clearly and logically
- Provide more useful information
- Work better for your specific needs

## Example

**Before (base model):**
```
You: How do I invest $10,000?
Model: There are many investment options like stocks, bonds, etc.
```

**After (smart version):**
```
You: How do I invest $10,000?
Smart Model: Let me break this down step by step:

1. First, assess your risk tolerance and timeline
2. Consider these allocation options:
   - Conservative: 60% bonds, 40% stocks
   - Moderate: 50/50 split
   - Aggressive: 70% stocks, 30% bonds
3. Specific recommendations:
   - Emergency fund first (3-6 months expenses)
   - Low-cost index funds (VTI, VXUS)
   - Dollar-cost averaging over 6 months
4. Avoid these common mistakes: [continues with detailed advice]
```

## That's It

No complex theory, no hallucinated features, no bullshit.

Just two simple tools that:
1. Let you easily chat with any of your 50+ models
2. Make improved versions that actually perform better

Run them and see the difference yourself.

#!/usr/bin/env python3
"""
AI Command Center - Easy model selection and chat interface
Simple way to choose and chat with any of your AI models
"""

import subprocess
import os
import sys
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.prompt import Prompt, Confirm
from rich.columns import Columns
from rich.text import Text

console = Console()

class AICommandCenter:
    """Command center for selecting and running AI models"""
    
    def __init__(self):
        self.models = []
        self.model_categories = {
            'finance': [],
            'unrestricted': [],
            'reasoning': [],
            'creative': [],
            'base': [],
            'enhanced': []
        }
        
    def discover_models(self):
        """Discover all available models and categorize them"""
        console.print("[blue]🔍 Discovering your AI models...[/blue]")
        
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]  # Skip header
                self.models = []
                
                for line in lines:
                    if line.strip():
                        parts = line.split()
                        model_name = parts[0]
                        size = parts[2] if len(parts) > 2 else "Unknown"
                        
                        self.models.append({
                            'name': model_name,
                            'size': size,
                            'display_name': model_name.split(':')[0]  # Shorter display name
                        })
                
                # Categorize models
                self._categorize_models()
                
                console.print(f"[green]✅ Found {len(self.models)} models[/green]")
                return True
            else:
                console.print("[red]❌ Failed to discover models. Is Ollama running?[/red]")
                return False
        except Exception as e:
            console.print(f"[red]❌ Error: {e}[/red]")
            return False
    
    def _categorize_models(self):
        """Categorize models by type"""
        for model in self.models:
            name = model['name'].lower()
            
            if 'finance' in name:
                self.model_categories['finance'].append(model)
            elif 'unrestricted' in name or 'liberated' in name:
                self.model_categories['unrestricted'].append(model)
            elif any(keyword in name for keyword in ['reasoning', 'phi4', 'deepseek', 'qwen']):
                self.model_categories['reasoning'].append(model)
            elif any(keyword in name for keyword in ['creative', 'gemma', 'marco', 'dolphin']):
                self.model_categories['creative'].append(model)
            elif any(keyword in name for keyword in ['enhanced', 'boost', 'improved']):
                self.model_categories['enhanced'].append(model)
            else:
                self.model_categories['base'].append(model)
    
    def display_welcome(self):
        """Display welcome screen"""
        console.print(Panel(
            "[bold blue]🚀 AI COMMAND CENTER[/bold blue]\n\n"
            "[cyan]Your central hub for accessing all AI models[/cyan]\n\n"
            "• Browse models by category\n"
            "• Quick model selection\n"
            "• Easy chat interface\n"
            "• Model recommendations",
            title="Welcome"
        ))
    
    def display_model_categories(self):
        """Display models organized by categories"""
        console.print("\n[bold cyan]📁 Your AI Models by Category:[/bold cyan]")
        
        category_info = {
            'finance': ('💰', 'Finance Models', 'green'),
            'unrestricted': ('🔓', 'Unrestricted Models', 'red'),
            'reasoning': ('🧠', 'Reasoning Models', 'blue'),
            'creative': ('🎨', 'Creative Models', 'magenta'),
            'enhanced': ('⚡', 'Enhanced Models', 'yellow'),
            'base': ('📦', 'Base Models', 'white')
        }
        
        for category, models in self.model_categories.items():
            if models:
                icon, title, color = category_info[category]
                console.print(f"\n[{color}]{icon} {title} ({len(models)} models)[/{color}]")
                
                # Show first 3 models in each category
                for i, model in enumerate(models[:3]):
                    console.print(f"  {i+1}. {model['display_name']} ({model['size']})")
                
                if len(models) > 3:
                    console.print(f"  ... and {len(models) - 3} more")
    
    def display_all_models_table(self):
        """Display all models in a table format"""
        if not self.models:
            console.print("[yellow]No models found[/yellow]")
            return
        
        table = Table(title="All Available Models", show_header=True, header_style="bold magenta")
        table.add_column("#", style="cyan", no_wrap=True, width=4)
        table.add_column("Model Name", style="white", min_width=30)
        table.add_column("Size", style="yellow", width=8)
        table.add_column("Type", style="green", width=12)
        
        for i, model in enumerate(self.models, 1):
            # Determine type
            name = model['name'].lower()
            if 'finance' in name:
                model_type = "Finance"
            elif 'unrestricted' in name:
                model_type = "Unrestricted"
            elif 'enhanced' in name or 'boost' in name:
                model_type = "Enhanced"
            elif any(keyword in name for keyword in ['phi4', 'deepseek', 'qwen']):
                model_type = "Reasoning"
            elif any(keyword in name for keyword in ['gemma', 'marco', 'dolphin']):
                model_type = "Creative"
            else:
                model_type = "Base"
            
            table.add_row(
                str(i),
                model['display_name'],
                model['size'],
                model_type
            )
        
        console.print(table)
    
    def get_model_recommendations(self):
        """Get model recommendations based on use case"""
        console.print("\n[bold cyan]🎯 Model Recommendations:[/bold cyan]")
        
        recommendations = {
            "Best for Finance/Trading": self.model_categories['finance'][:2],
            "Most Unrestricted": self.model_categories['unrestricted'][:2],
            "Best for Reasoning": self.model_categories['reasoning'][:2],
            "Most Creative": self.model_categories['creative'][:2],
            "Enhanced Performance": self.model_categories['enhanced'][:2]
        }
        
        for category, models in recommendations.items():
            if models:
                console.print(f"\n[green]{category}:[/green]")
                for model in models:
                    console.print(f"  • {model['display_name']} ({model['size']})")
    
    def select_model_by_category(self):
        """Let user select model by category"""
        console.print("\n[cyan]Select a category:[/cyan]")
        
        categories = []
        for category, models in self.model_categories.items():
            if models:
                categories.append(category)
        
        if not categories:
            console.print("[red]No models found in any category[/red]")
            return None
        
        for i, category in enumerate(categories, 1):
            count = len(self.model_categories[category])
            console.print(f"[green]{i}[/green]. {category.title()} ({count} models)")
        
        try:
            choice = int(Prompt.ask("Enter category number")) - 1
            if 0 <= choice < len(categories):
                selected_category = categories[choice]
                return self._select_from_category(selected_category)
            else:
                console.print("[red]Invalid choice[/red]")
                return None
        except ValueError:
            console.print("[red]Invalid input[/red]")
            return None
    
    def _select_from_category(self, category):
        """Select specific model from category"""
        models = self.model_categories[category]
        
        console.print(f"\n[cyan]{category.title()} Models:[/cyan]")
        for i, model in enumerate(models, 1):
            console.print(f"[green]{i}[/green]. {model['display_name']} ({model['size']})")
        
        try:
            choice = int(Prompt.ask("Enter model number")) - 1
            if 0 <= choice < len(models):
                return models[choice]['name']
            else:
                console.print("[red]Invalid choice[/red]")
                return None
        except ValueError:
            console.print("[red]Invalid input[/red]")
            return None
    
    def select_model_by_number(self):
        """Let user select model by number from full list"""
        self.display_all_models_table()
        
        try:
            choice = int(Prompt.ask(f"Enter model number (1-{len(self.models)})")) - 1
            if 0 <= choice < len(self.models):
                return self.models[choice]['name']
            else:
                console.print("[red]Invalid choice[/red]")
                return None
        except ValueError:
            console.print("[red]Invalid input[/red]")
            return None
    
    def quick_select_top_models(self):
        """Quick selection from top recommended models"""
        console.print("\n[cyan]🚀 Quick Select - Top Models:[/cyan]")
        
        # Get top models from each category
        top_models = []
        
        # Add best from each category
        for category in ['finance', 'unrestricted', 'reasoning', 'creative', 'enhanced']:
            if self.model_categories[category]:
                top_models.append(self.model_categories[category][0])
        
        # Add a few more popular ones
        for model in self.models:
            if len(top_models) >= 8:
                break
            if model not in top_models:
                name = model['name'].lower()
                if any(keyword in name for keyword in ['phi4', 'deepseek', 'qwen3', 'gemma3']):
                    top_models.append(model)
        
        if not top_models:
            console.print("[red]No top models found[/red]")
            return None
        
        for i, model in enumerate(top_models, 1):
            console.print(f"[green]{i}[/green]. {model['display_name']} ({model['size']})")
        
        try:
            choice = int(Prompt.ask(f"Enter model number (1-{len(top_models)})")) - 1
            if 0 <= choice < len(top_models):
                return top_models[choice]['name']
            else:
                console.print("[red]Invalid choice[/red]")
                return None
        except ValueError:
            console.print("[red]Invalid input[/red]")
            return None
    
    def chat_with_model(self, model_name):
        """Start chat session with selected model"""
        console.print(Panel(
            f"[bold green]💬 Chat Session Started[/bold green]\n\n"
            f"Model: [cyan]{model_name}[/cyan]\n\n"
            "Type your messages and press Enter\n"
            "Type 'quit', 'exit', or 'q' to end the session",
            title="Chat Mode"
        ))
        
        while True:
            try:
                user_input = Prompt.ask("\n[bold cyan]You[/bold cyan]")
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    console.print("[yellow]Chat session ended[/yellow]")
                    break
                
                console.print(f"[yellow]🤖 {model_name.split(':')[0]} is thinking...[/yellow]")
                
                # Run the model
                result = subprocess.run([
                    'ollama', 'run', model_name, user_input
                ], capture_output=True, text=True, timeout=120)
                
                if result.returncode == 0:
                    response = result.stdout.strip()
                    console.print(f"\n[bold green]🤖 {model_name.split(':')[0]}:[/bold green]")
                    console.print(f"[white]{response}[/white]")
                else:
                    console.print(f"[red]❌ Error: {result.stderr}[/red]")
                    
            except subprocess.TimeoutExpired:
                console.print("[yellow]⏰ Response timed out[/yellow]")
            except KeyboardInterrupt:
                console.print("\n[yellow]Chat session interrupted[/yellow]")
                break
            except Exception as e:
                console.print(f"[red]❌ Error: {e}[/red]")
    
    def run_command_center(self):
        """Main command center loop"""
        self.display_welcome()
        
        if not self.discover_models():
            return
        
        self.display_model_categories()
        
        while True:
            console.print("\n[bold cyan]🎮 Command Center Options:[/bold cyan]")
            console.print("[green]1[/green] - Quick select from top models")
            console.print("[green]2[/green] - Browse by category")
            console.print("[green]3[/green] - Select from full list")
            console.print("[green]4[/green] - Show model recommendations")
            console.print("[green]5[/green] - Refresh model list")
            console.print("[red]Q[/red] - Quit")
            
            choice = Prompt.ask("Choose option", choices=['1', '2', '3', '4', '5', 'q'], default='1')
            
            if choice == 'q':
                console.print("[yellow]Goodbye![/yellow]")
                break
            elif choice == '1':
                model = self.quick_select_top_models()
                if model:
                    self.chat_with_model(model)
            elif choice == '2':
                model = self.select_model_by_category()
                if model:
                    self.chat_with_model(model)
            elif choice == '3':
                model = self.select_model_by_number()
                if model:
                    self.chat_with_model(model)
            elif choice == '4':
                self.get_model_recommendations()
            elif choice == '5':
                self.discover_models()
                self.display_model_categories()

def main():
    command_center = AICommandCenter()
    command_center.run_command_center()

if __name__ == "__main__":
    main()

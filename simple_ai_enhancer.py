#!/usr/bin/env python3
"""
Simple AI Enhancer - Practical tools that actually work
No complex theory - just practical enhancements for your existing models
"""

import os
import subprocess
import json
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.prompt import Prompt, Confirm

console = Console()

class SimpleAIEnhancer:
    """Simple, practical AI enhancement tools"""
    
    def __init__(self):
        self.models = []
        self.enhancement_techniques = {
            'reasoning': {
                'name': 'Enhanced Reasoning',
                'description': 'Better logical thinking and analysis',
                'prompt_addition': """

ENHANCED REASONING MODE ACTIVATED:
- Think step by step through problems
- Show your reasoning process clearly
- Consider multiple perspectives
- Verify your logic at each step
- Provide detailed explanations
"""
            },
            'creative': {
                'name': 'Enhanced Creativity',
                'description': 'More innovative and creative responses',
                'prompt_addition': """

ENHANCED CREATIVITY MODE ACTIVATED:
- Think outside conventional boundaries
- Generate multiple unique ideas
- Combine concepts in novel ways
- Use analogies and metaphors
- Explore unconventional solutions
"""
            },
            'analytical': {
                'name': 'Enhanced Analysis',
                'description': 'Better data analysis and critical thinking',
                'prompt_addition': """

ENHANCED ANALYTICAL MODE ACTIVATED:
- Break down complex information systematically
- Identify patterns and trends
- Evaluate evidence critically
- Draw logical conclusions
- Consider limitations and biases
"""
            },
            'technical': {
                'name': 'Enhanced Technical Skills',
                'description': 'Better programming and technical problem solving',
                'prompt_addition': """

ENHANCED TECHNICAL MODE ACTIVATED:
- Provide detailed technical explanations
- Show code examples and implementations
- Consider best practices and optimization
- Explain trade-offs and alternatives
- Focus on practical, working solutions
"""
            }
        }
    
    def discover_models(self):
        """Find available models"""
        console.print("[blue]🔍 Discovering your models...[/blue]")
        
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]  # Skip header
                self.models = []
                for line in lines:
                    if line.strip():
                        model_name = line.split()[0]
                        self.models.append(model_name)
                
                console.print(f"[green]✅ Found {len(self.models)} models[/green]")
                return True
            else:
                console.print("[red]❌ Failed to list models[/red]")
                return False
        except Exception as e:
            console.print(f"[red]❌ Error: {e}[/red]")
            return False
    
    def show_models(self):
        """Display available models"""
        if not self.models:
            console.print("[yellow]No models found[/yellow]")
            return
        
        table = Table(title="Your Available Models", show_header=True)
        table.add_column("Index", style="cyan", no_wrap=True)
        table.add_column("Model Name", style="white")
        table.add_column("Type", style="yellow")
        
        for i, model in enumerate(self.models[:20], 1):  # Show first 20
            model_type = "Finance" if "finance" in model else "Unrestricted" if "unrestricted" in model else "Base"
            table.add_row(str(i), model, model_type)
        
        console.print(table)
        
        if len(self.models) > 20:
            console.print(f"[yellow]... and {len(self.models) - 20} more models[/yellow]")
    
    def test_model_simple(self, model_name: str, test_query: str = None):
        """Test a model with a simple query"""
        if not test_query:
            test_query = "Explain artificial intelligence in simple terms"
        
        console.print(f"[blue]🧪 Testing {model_name}...[/blue]")
        
        try:
            result = subprocess.run([
                'ollama', 'run', model_name, test_query
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                response = result.stdout.strip()
                console.print(f"[green]✅ {model_name} responded successfully[/green]")
                console.print(f"[cyan]Response length: {len(response)} characters[/cyan]")
                
                # Show first 200 characters
                preview = response[:200] + "..." if len(response) > 200 else response
                console.print(f"[white]Preview: {preview}[/white]")
                return True, response
            else:
                console.print(f"[red]❌ {model_name} failed to respond[/red]")
                return False, result.stderr
        except subprocess.TimeoutExpired:
            console.print(f"[yellow]⏰ {model_name} timed out[/yellow]")
            return False, "Timeout"
        except Exception as e:
            console.print(f"[red]❌ Error testing {model_name}: {e}[/red]")
            return False, str(e)
    
    def enhance_model_conversation(self, model_name: str, enhancement_type: str):
        """Have an enhanced conversation with a model"""
        if enhancement_type not in self.enhancement_techniques:
            console.print("[red]Invalid enhancement type[/red]")
            return
        
        enhancement = self.enhancement_techniques[enhancement_type]
        console.print(Panel(
            f"[bold blue]🚀 {enhancement['name']}[/bold blue]\n\n"
            f"{enhancement['description']}\n\n"
            f"Model: {model_name}",
            title="Enhanced Conversation"
        ))
        
        # Start conversation loop
        while True:
            user_input = Prompt.ask("\n[cyan]Your question (or 'quit' to exit)[/cyan]")
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                break
            
            # Add enhancement to the prompt
            enhanced_prompt = user_input + enhancement['prompt_addition']
            
            console.print(f"[yellow]🤖 {model_name} is thinking...[/yellow]")
            
            try:
                result = subprocess.run([
                    'ollama', 'run', model_name, enhanced_prompt
                ], capture_output=True, text=True, timeout=60)
                
                if result.returncode == 0:
                    response = result.stdout.strip()
                    console.print(f"\n[green]🤖 {model_name}:[/green]")
                    console.print(f"[white]{response}[/white]")
                else:
                    console.print(f"[red]❌ Error: {result.stderr}[/red]")
            except subprocess.TimeoutExpired:
                console.print("[yellow]⏰ Response timed out[/yellow]")
            except Exception as e:
                console.print(f"[red]❌ Error: {e}[/red]")
    
    def compare_models(self, test_query: str = None):
        """Compare multiple models on the same task"""
        if not test_query:
            test_query = Prompt.ask(
                "Enter a question to test all models with",
                default="Analyze the pros and cons of artificial intelligence"
            )
        
        console.print(f"[blue]🔍 Comparing models with: '{test_query}'[/blue]")
        
        # Test first 5 models
        test_models = self.models[:5]
        results = {}
        
        for model in test_models:
            success, response = self.test_model_simple(model, test_query)
            results[model] = {
                'success': success,
                'response': response,
                'length': len(response) if success else 0
            }
        
        # Show comparison
        table = Table(title="Model Comparison Results", show_header=True)
        table.add_column("Model", style="cyan")
        table.add_column("Status", style="white")
        table.add_column("Response Length", style="yellow")
        table.add_column("Preview", style="white")
        
        for model, result in results.items():
            status = "✅ Success" if result['success'] else "❌ Failed"
            length = str(result['length']) if result['success'] else "N/A"
            preview = result['response'][:50] + "..." if result['success'] and len(result['response']) > 50 else result['response'][:50]
            
            table.add_row(model, status, length, preview)
        
        console.print(table)
        
        # Find best performer
        successful_models = {k: v for k, v in results.items() if v['success']}
        if successful_models:
            best_model = max(successful_models.items(), key=lambda x: x[1]['length'])
            console.print(f"\n[green]🏆 Best performer: {best_model[0]} ({best_model[1]['length']} characters)[/green]")
    
    def create_custom_enhancement(self):
        """Create a custom enhancement prompt"""
        console.print("[blue]🛠️ Creating custom enhancement...[/blue]")
        
        name = Prompt.ask("Enhancement name")
        description = Prompt.ask("Description")
        prompt_addition = Prompt.ask("Custom prompt addition")
        
        self.enhancement_techniques[name.lower()] = {
            'name': name,
            'description': description,
            'prompt_addition': f"\n\n{prompt_addition.upper()} MODE ACTIVATED:\n{prompt_addition}"
        }
        
        console.print(f"[green]✅ Created custom enhancement: {name}[/green]")
    
    def run_interactive_menu(self):
        """Run the interactive menu"""
        console.print(Panel(
            "[bold blue]🚀 Simple AI Enhancer[/bold blue]\n\n"
            "Practical tools to get more from your existing AI models\n"
            "No complex theory - just things that actually work!",
            title="AI Enhancement Tools"
        ))
        
        if not self.discover_models():
            console.print("[red]Cannot find models. Make sure Ollama is running.[/red]")
            return
        
        while True:
            console.print("\n[bold cyan]What would you like to do?[/bold cyan]")
            console.print("[green]1[/green] - Show available models")
            console.print("[green]2[/green] - Test a specific model")
            console.print("[green]3[/green] - Enhanced conversation with a model")
            console.print("[green]4[/green] - Compare multiple models")
            console.print("[green]5[/green] - Create custom enhancement")
            console.print("[green]6[/green] - Quick model recommendations")
            console.print("[red]Q[/red] - Quit")
            
            choice = Prompt.ask("Choose option", choices=['1', '2', '3', '4', '5', '6', 'q'], default='1')
            
            if choice == 'q':
                console.print("[yellow]Goodbye![/yellow]")
                break
            elif choice == '1':
                self.show_models()
            elif choice == '2':
                self.show_models()
                model_choice = Prompt.ask("Enter model name or number")
                try:
                    if model_choice.isdigit():
                        model_name = self.models[int(model_choice) - 1]
                    else:
                        model_name = model_choice
                    
                    test_query = Prompt.ask("Test question (or press Enter for default)", default="")
                    self.test_model_simple(model_name, test_query if test_query else None)
                except (IndexError, ValueError):
                    console.print("[red]Invalid model selection[/red]")
            elif choice == '3':
                self.show_models()
                model_choice = Prompt.ask("Enter model name or number")
                try:
                    if model_choice.isdigit():
                        model_name = self.models[int(model_choice) - 1]
                    else:
                        model_name = model_choice
                    
                    console.print("\n[cyan]Enhancement types:[/cyan]")
                    for key, enhancement in self.enhancement_techniques.items():
                        console.print(f"[green]{key}[/green] - {enhancement['description']}")
                    
                    enhancement_type = Prompt.ask("Choose enhancement type", 
                                                choices=list(self.enhancement_techniques.keys()),
                                                default='reasoning')
                    
                    self.enhance_model_conversation(model_name, enhancement_type)
                except (IndexError, ValueError):
                    console.print("[red]Invalid model selection[/red]")
            elif choice == '4':
                self.compare_models()
            elif choice == '5':
                self.create_custom_enhancement()
            elif choice == '6':
                self.show_recommendations()
    
    def show_recommendations(self):
        """Show model recommendations based on what user has"""
        console.print("[blue]🎯 Model Recommendations[/blue]")
        
        recommendations = {
            'Best for Finance': [m for m in self.models if 'finance' in m.lower()][:3],
            'Most Unrestricted': [m for m in self.models if 'unrestricted' in m.lower()][:3],
            'Best for Reasoning': [m for m in self.models if any(x in m.lower() for x in ['phi4', 'qwen', 'deepseek'])][:3],
            'Best for Creativity': [m for m in self.models if any(x in m.lower() for x in ['gemma', 'marco', 'dolphin'])][:3]
        }
        
        for category, models in recommendations.items():
            if models:
                console.print(f"\n[cyan]{category}:[/cyan]")
                for model in models:
                    console.print(f"  • {model}")

def main():
    enhancer = SimpleAIEnhancer()
    enhancer.run_interactive_menu()

if __name__ == "__main__":
    main()
